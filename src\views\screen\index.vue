<!--
 * @Description: 云控中心大屏
 * @Author: XIAOLIJUN
 * @Date: 2025-08-18 17:58:52
 * @LastEditors: XIAOLIJUN
 * @LastEditTime: 2025-08-19 17:16:29
-->
<template>
  <div :class="prefixCls">
    <div :class="`${prefixCls}-container`" :style="containerStyle">
      <div :class="`${prefixCls}-header`">
        <div :class="`${prefixCls}-header-text`">中国 · 吉林</div>
      </div>
      <div :class="`${prefixCls}-body`">
        <div :class="`${prefixCls}-body-left`"></div>
        <div :class="`${prefixCls}-body-center`">
          <!-- 地图组件 -->
          <!-- <ScreenMap ref="screenMapRef" /> -->
          <div id="cesiumContainer" ref="cesiumContainer" class="w-full h-full"></div>
        </div>
        <div :class="`${prefixCls}-body-right`"></div>
      </div>
      <div :class="`${prefixCls}-footer`">
        <img :src="footerPng" />
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
  import { ref, onMounted, provide } from 'vue';
  import { useDesign } from '/@/hooks/web/useDesign';
  import { useScreenScale } from '/@/hooks/web/useScreenScale';
  // import ScreenMap from './components/ScreenMap.vue';
  import footerPng from '/@/assets/images/layout/layout_footer.png';

  import * as Cesium from 'cesium';
  import 'cesium/Build/Cesium/Widgets/widgets.css';

  const { prefixCls } = useDesign('screen');

  // 使用大屏缩放 Hook
  const {
    scale,
    screenWidth,
    screenHeight,
    containerStyle,
    updateScale,
    getScale,
    getDesignSize,
    onMapResize,
    offMapResize,
  } = useScreenScale({
    designWidth: 8160,
    designHeight: 1860,
    debounceTime: 100,
    enableMapResize: true,
  });

  // DOM 引用
  const screenMapRef = ref<any>();
  const cesiumContainer = ref<any>();

  // 地图实例引用（示例）
  const mapInstance = ref<any>(null);

  // 向子组件提供缩放数据
  provide('scale', scale);
  provide('screenWidth', screenWidth);
  provide('screenHeight', screenHeight);
  provide('onMapResize', onMapResize);
  provide('offMapResize', offMapResize);

  // 处理地图缩放的示例函数
  const handleMapResize = (currentScale: number) => {
    console.log('地图缩放比例更新:', currentScale);

    // 通知地图组件刷新
    if (screenMapRef.value && screenMapRef.value.refreshMap) {
      screenMapRef.value.refreshMap();
    }

    // 示例：如果有 Cesium 地图实例
    if (mapInstance.value && mapInstance.value.resize) {
      try {
        mapInstance.value.resize();
        // 根据缩放比例调整渲染质量
        if (currentScale < 0.5) {
          mapInstance.value.resolutionScale = Math.max(0.5, currentScale);
        } else {
          mapInstance.value.resolutionScale = 1.0;
        }
        mapInstance.value.scene?.requestRender?.();
      } catch (error) {
        console.warn('地图缩放适配失败:', error);
      }
    }
  };

  // 注册地图缩放回调
  onMounted(() => {
    onMapResize(handleMapResize);

    const viewer = new Cesium.Viewer('cesiumContainer', {
      animation: false,
      baseLayerPicker: false,
      fullscreenButton: false,
      timeline: false,
      infoBox: false,
    });

    // viewer.imageryLayers.addImageryProvider(
    //   new Cesium.UrlTemplateImageryProvider({
    //     url: 'https://webrd02.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=2&style=8&x={x}&y={y}&z={z}',
    //     minimumLevel: 0,
    //     maximumLevel: 18,
    //   }),
    // );
  });

  // 暴露方法供外部调用
  defineExpose({
    updateScale,
    getScale,
    getDesignSize,
    scale,
    screenWidth,
    screenHeight,
    containerStyle,
    // 地图相关方法
    setMapInstance: (instance: any) => {
      mapInstance.value = instance;
    },
    onMapResize,
    offMapResize,
  });
</script>
<style lang="less">
  @prefix-cls: ~'@{namespace}-screen';

  .@{prefix-cls} {
    height: 100vh;
    width: 100vw;
    background-color: rgba(0, 25, 52, 1);
    overflow: hidden;
    position: relative;

    &-container {
      background-image: url('/@/assets/images/layout/layout_bg.png');
      background-size: cover;
      background-position: center;
    }

    &-header {
      height: 226px;
      background: url('/@/assets/images/layout/layout_title.png');
      background-size: cover;
      background-position: center;
      display: flex;
      align-items: center;
      position: relative;
      padding: 0 80px;

      &-text {
        color: #eaf2f2;
        font-size: 60px;
        font-weight: 700;
        letter-spacing: 4.29px;
      }
    }

    &-body {
      height: calc(100% - 326px);
      display: flex;

      &-left,
      &-right {
        width: 2720px;
        border: 1px solid rgba(64, 158, 255, 0.1);
        position: relative;
        padding: 0 40px;
      }

      &-center {
        flex: 1;
        background: url('/@/assets/images/layout/layout_box_center.png');
        background-size: cover;
        background-position: center;
        padding: 12px;
        border-radius: 12px;
        position: relative;
        overflow: hidden;
      }
    }

    &-footer {
      height: 100px;
      position: relative;

      img {
        position: absolute;
        width: 100%;
        bottom: 0;
      }
    }

    &-map-container {
      width: 100%;
      height: 100%;
      position: relative;
      // 确保地图容器在缩放时保持正确的渲染
      transform-style: preserve-3d;
    }
  }

  // 确保在缩放时文字和图标保持清晰
  .@{prefix-cls}-container * {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
</style>
