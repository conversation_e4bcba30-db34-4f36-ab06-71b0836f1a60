import type { UserConfig, ConfigEnv } from 'vite';
import pkg from './package.json';
import dayjs from 'dayjs';
import { loadEnv } from 'vite';
import { resolve } from 'path';
import { generateModifyVars } from './build/generate/generateModifyVars';
import { createProxy } from './build/vite/proxy';
import { wrapperEnv } from './build/utils';
import { createVitePlugins } from './build/vite/plugin';
import { OUTPUT_DIR } from './build/constant';
import cesium from 'vite-plugin-cesium';

function pathResolve(dir: string) {
  return resolve(process.cwd(), '.', dir);
}

const { dependencies, devDependencies, name, version } = pkg;
const __APP_INFO__ = {
  pkg: { dependencies, devDependencies, name, version },
  lastBuildTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
};

export default ({ command, mode }: ConfigEnv): UserConfig => {
  const root = process.cwd();

  const env = loadEnv(mode, root);

  // loadEnv读取的布尔类型是一个字符串。此函数可转换为布尔类型
  const viteEnv = wrapperEnv(env);

  const { VITE_PORT, VITE_PUBLIC_PATH, VITE_PROXY, VITE_DROP_CONSOLE } = viteEnv;

  const isBuild = command === 'build';

  return {
    base: VITE_PUBLIC_PATH,
    root,
    resolve: {
      alias: [
        {
          find: 'vue-i18n',
          replacement: 'vue-i18n/dist/vue-i18n.cjs.js',
        },
        // /@/xxxx => src/xxxx
        {
          find: /\/@\//,
          replacement: pathResolve('src') + '/',
        },
        // /#/xxxx => types/xxxx
        {
          find: /\/#\//,
          replacement: pathResolve('types') + '/',
        },
      ],
    },
    server: {
      https: false, // 开启https
      host: true, // 监听所有本地IP
      port: VITE_PORT,
      headers: { 'Access-Control-Allow-Origin': '*' }, // 解决集成到统一门户跨域问题
      proxy: createProxy(VITE_PROXY, viteEnv), // 从.env加载代理配置
    },
    esbuild: {
      target: 'es2020', // 设置 esbuild 目标环境
      pure: VITE_DROP_CONSOLE ? ['console.log', 'debugger'] : [],
    },
    build: {
      target: 'es2020', // 更新目标环境以支持现代 JavaScript 特性
      cssTarget: 'chrome80',
      outDir: OUTPUT_DIR,
      minify: 'terser',
      /**
       * 当 minify=“minify:'terser'” 解开注释
       * Uncomment when minify="minify:'terser'"
       */
      terserOptions: {
        compress: {
          keep_infinity: true,
          drop_console: VITE_DROP_CONSOLE,
        },
      },
      rollupOptions: {
        output: {
          format: 'es',
        },
      },
      brotliSize: false, // 关闭brotliSize显示可以稍微减少包装时间
      chunkSizeWarningLimit: 2000, // 超大静态资源警告门槛 2000kb
    },
    define: {
      // 设置vue-i18-next
      // 抑制警告
      __INTLIFY_PROD_DEVTOOLS__: false,
      __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: !isBuild, // 启用详细的水合错误信息
      __APP_INFO__: JSON.stringify(__APP_INFO__),
      // Cesium global variables
      CESIUM_BASE_URL: JSON.stringify('./'),
    },

    css: {
      preprocessorOptions: {
        less: { modifyVars: generateModifyVars(), javascriptEnabled: true },
      },
    },

    // The vite plugin used by the project. The quantity is large, so it is separately extracted and managed
    plugins: [...createVitePlugins(viteEnv, isBuild), cesium()],

    optimizeDeps: {
      // @iconify/iconify: The dependency is dynamically and virtually loaded by @purge-icons/generated, so it needs to be specified explicitly
      include: [
        '@vue/runtime-core',
        '@vue/shared',
        '@iconify/iconify',
        'ant-design-vue/es/locale/zh_CN',
        'ant-design-vue/es/locale/en_US',
      ],
      // Exclude Cesium and related packages from pre-bundling to avoid optimization issues
      exclude: ['cesium', 'vue-cesium', '@zip.js/zip.js', '@spz-loader/core'],
      // 设置 esbuild 目标环境
      esbuildOptions: {
        target: 'es2020',
      },
    },
  };
};
